#!/usr/bin/env -S deno run --allow-read --allow-write

import { DB } from "https://deno.land/x/sqlite@v3.9.1/mod.ts";

/**
 * Deno script to create a view named "console_information_schema_table" 
 * in the resource-surveillance.sqlite.db database
 */

const DB_PATH = "./resource-surveillance.sqlite.db";

async function createConsoleInformationSchemaTableView() {
  console.log("🔧 Opening database:", DB_PATH);
  
  try {
    // Open the SQLite database
    const db = new DB(DB_PATH);    
    
    // Drop the view if it already exists
    db.execute(`DROP VIEW IF EXISTS console_information_schema_table`);
    
    // Create the console_information_schema_table view
    // This view provides information about all tables and their columns
    const createViewSQL = `
      CREATE VIEW console_information_schema_table AS
      SELECT
          tbl.name AS table_name,
          col.name AS column_name,
          col.type AS data_type,
          CASE WHEN col.pk = 1 THEN 'Yes' ELSE 'No' END AS is_primary_key,
          CASE WHEN col."notnull" = 1 THEN 'Yes' ELSE 'No' END AS is_not_null,
          col.dflt_value AS default_value,
          'console/info-schema/table.sql?name=' || tbl.name || '&stats=yes' as info_schema_web_ui_path,
          '[Content](console/info-schema/table.sql?name=' || tbl.name || '&stats=yes)' as info_schema_link_abbrev_md,
          '[' || tbl.name || ' (table) Schema](console/info-schema/table.sql?name=' || tbl.name || '&stats=yes)' as info_schema_link_full_md,
          'console/content/table/' || tbl.name || '.sql?stats=yes' as content_web_ui_path,
          '[Content]($SITE_PREFIX_URL/console/content/table/' || tbl.name || '.sql?stats=yes)' as content_web_ui_link_abbrev_md,
          '[' || tbl.name || ' (table) Content](console/content/table/' || tbl.name || '.sql?stats=yes)' as content_web_ui_link_full_md,
          tbl.sql as sql_ddl
      FROM sqlite_master tbl
      JOIN pragma_table_info(tbl.name) col
      WHERE tbl.type = 'table' AND tbl.name NOT LIKE 'sqlite_%';

      DROP VIEW IF EXISTS console_information_schema_table_col_fkey;

      CREATE VIEW console_information_schema_table_col_fkey AS
      SELECT
          tbl.name AS table_name,
          f."from" AS column_name,
          f."from" || ' references ' || f."table" || '.' || f."to" AS foreign_key
      FROM sqlite_master tbl
      JOIN pragma_foreign_key_list(tbl.name) f
      WHERE tbl.type = 'table' AND tbl.name NOT LIKE 'sqlite_%';

    DROP VIEW IF EXISTS console_information_schema_table_col_index;

    CREATE VIEW console_information_schema_table_col_index AS
    SELECT
        tbl.name AS table_name,
        pi.name AS column_name,
        idx.name AS index_name
    FROM sqlite_master tbl
    JOIN pragma_index_list(tbl.name) idx
    JOIN pragma_index_info(idx.name) pi
    WHERE tbl.type = 'table' AND tbl.name NOT LIKE 'sqlite_%';

    DELETE FROM sqlpage_files;

    -- Insert a new file record into sqlpage_files
  INSERT INTO sqlpage_files (path, contents, last_modified)
  VALUES (
    'index.sql',
    '
select 
    ''breadcrumb'' as component;
select 
    ''Home'' as title,
    ''/''    as link;

  SELECT
        ''card''                      as component,
        ''Information Schema'' as title,
        1                           as columns;

  SELECT ''title'' AS component, ''Tables'' as contents;
  SELECT ''table'' AS component,
        ''Table'' AS markdown,
        ''Column Count'' as align_right,
        ''Content'' as markdown,
        TRUE as sort,
        TRUE as search;
  SELECT
      ''['' || table_name || ''](table.sql?name='' || table_name || '')'' AS "Table",
      COUNT(column_name) AS "Column Count"
  FROM console_information_schema_table
  GROUP BY table_name;
    ',
    CURRENT_TIMESTAMP
  );

    -- Insert the new file record into sqlpage_files
    INSERT INTO sqlpage_files (path, contents, last_modified)
    VALUES (
      'table.sql',
      '
select 
    ''breadcrumb'' as component;
select 
    ''Home'' as title,
    ''/''    as link;

    SELECT ''title'' AS component, $name AS contents;
    SELECT ''table'' AS component;
    SELECT
        column_name AS "Column",
        data_type AS "Type",
        is_primary_key AS "PK",
        is_not_null AS "Required",
        default_value AS "Default"
    FROM console_information_schema_table
    WHERE table_name = $name;

    SELECT ''title'' AS component, ''Foreign Keys'' as contents, 2 as level;
    SELECT ''table'' AS component;
    SELECT
        column_name AS "Column Name",
        foreign_key AS "Foreign Key"
    FROM console_information_schema_table_col_fkey
    WHERE table_name = $name;

    SELECT ''title'' AS component, ''Indexes'' as contents, 2 as level;
    SELECT ''table'' AS component;
    SELECT
        column_name AS "Column Name",
        index_name AS "Index Name"
    FROM console_information_schema_table_col_index
    WHERE table_name = $name;

    SELECT ''title'' AS component, ''SQL DDL'' as contents, 2 as level;
    SELECT ''code'' AS component;
    SELECT ''sql'' as language, (SELECT sql_ddl FROM console_information_schema_table WHERE table_name = $name) as contents;
      ',
      CURRENT_TIMESTAMP
    );

    `;
    
    db.execute(createViewSQL);
    
    console.log("✅ Successfully created console");
    
    
    
    // Close the database connection
    db.close();   
    
  } catch (error) {
    console.error("❌ Error creating view:", error);
    throw error;
  }
}

// Main execution
if (import.meta.main) {
  try {
    await createConsoleInformationSchemaTableView();
  } catch (error) {
    console.error("💥 Script failed:", error);
    Deno.exit(1);
  }
}
