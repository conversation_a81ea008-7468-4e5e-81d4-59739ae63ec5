   
SELECT
      'card'                      as component,
      'Information Schema' as title,
      1                           as columns;

              

SELECT 'title' AS component, 'Tables' as contents;
SELECT 'table' AS component,
      'Table' AS markdown,
      'Column Count' as align_right,
      'Content' as markdown,
      TRUE as sort,
      TRUE as search;
SELECT
    '[' || table_name || '](table.sql?name=' || table_name || ')' AS "Table",
    COUNT(column_name) AS "Column Count",
    REPLACE(content_web_ui_link_abbrev_md,'$SITE_PREFIX_URL',sqlpage.environment_variable('SQLPAGE_SITE_PREFIX') || '') as "Content"
FROM console_information_schema_table
GROUP BY table_name;


            